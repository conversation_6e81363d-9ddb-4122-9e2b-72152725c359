// 类型定义
import { CookieChangeInfo } from '../src/types'

interface Logger {
  info: (...args: any[]) => void
  error: (...args: any[]) => void
  success: (...args: any[]) => void
}

interface UserInfo {
  id: string
  [key: string]: any
}

interface Settings {
  defaultProvider: string
  defaultModel: string
}

interface ApiKeys {
  [provider: string]: string
}

interface StorageData {
  [key: string]: any
}

interface AiRequestData {
  provider: string
  model: string
  prompt: string
  apiKey?: string
  description: string
  formFields: any
  mode: string
  language: string
  projectId: string
  [key: string]: any
}

interface Message {
  type: string
  [key: string]: any
}

interface DefaultModels {
  openai: string
  claude: string
  moonshot: string
  gemini: string
  openrouter: string
  ollama: string
}

// Background script
export default defineBackground(() => {
  // Helper function for logging - disabled for production
  const Logger: Logger = {
    info: (...args) => {/* Production mode - logging disabled */},
    error: (...args) => console.error('[Formify Error]', ...args), // Keep error logging for critical issues
    success: (...args) => {/* Production mode - logging disabled */}
  }

  // Constants
  const STORAGE_KEYS = {
    SETTINGS: 'formify_settings',
    API_KEYS: 'formify_api_keys',
    VALIDATED_KEYS: 'formify_validated_keys',
    SKIP_LOGIN: 'formify_skip_login',
    TOKEN_STATS: 'formify_token_stats'
  } as const

  // API Base URL and Cookie Domain
  const API_BASE_URL = 'https://fillify-343190162770.asia-east1.run.app/api'
  const COOKIE_DOMAIN = 'fillify.tech'
  const COOKIE_URL = 'https://fillify.tech'

  // 全局登录状态管理
  let isLoggedIn = false
  let skipLogin = false

  // 初始化时检查是否已选择跳过登录
  chrome.storage.sync.get(STORAGE_KEYS.SKIP_LOGIN).then(({ formify_skip_login }) => {
    skipLogin = !!formify_skip_login
    // Skip login status initialized
  }).catch(error => {
    // Error loading skip login status
  })

  // 用于追踪最后一次用户信息更新时间
  let lastUserInfoFetch = 0
  const USER_INFO_MAX_AGE = 5 * 60 * 1000 // 用户信息最大缓存时间（5分钟）

  // Provider Models Configuration
  const PROVIDER_MODELS = {
    openai: [
      { id: 'gpt-4', name: 'GPT-4' },
      { id: 'gpt-4-turbo-preview', name: 'GPT-4 Turbo' },
      { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo' }
    ],
    claude: [
      { id: 'claude-3-opus-20240229', name: 'Claude-3 Opus' },
      { id: 'claude-3-sonnet-20240229', name: 'Claude-3 Sonnet' },
      { id: 'claude-2.1', name: 'Claude-2.1' }
    ],
    moonshot: [
      { id: 'moonshot-v1-8k', name: 'Moonshot V1 (8K)' },
      { id: 'moonshot-v1-32k', name: 'Moonshot V1 (32K)' },
      { id: 'moonshot-v1-128k', name: 'Moonshot V1 (128K)' }
    ],
    gemini: [
      { id: 'gemini-2.0-flash', name: 'Gemini 2.0 Flash' },
      { id: 'gemini-2.0-flash-lite', name: 'Gemini 2.0 Flash Lite' },
      { id: 'gemini-1.5-flash', name: 'Gemini 1.5 Flash' },
      { id: 'gemini-1.5-pro', name: 'Gemini 1.5 Pro' }
    ],
    openrouter: [
      { id: 'openai/gpt-3.5-turbo', name: 'GPT-3.5 Turbo' },
      { id: 'openai/gpt-4', name: 'GPT-4' },
      { id: 'openai/gpt-4-turbo', name: 'GPT-4 Turbo' },
      { id: 'anthropic/claude-3-opus', name: 'Claude-3 Opus' },
      { id: 'anthropic/claude-3-sonnet', name: 'Claude-3 Sonnet' },
      { id: 'meta-llama/llama-3-70b-instruct', name: 'Llama-3 70B' },
      { id: 'mistralai/mistral-large', name: 'Mistral Large' }
    ],
    ollama: [
      { id: 'llama2', name: 'Llama 2' },
      { id: 'llama2:13b', name: 'Llama 2 13B' },
      { id: 'llama2:70b', name: 'Llama 2 70B' },
      { id: 'mistral', name: 'Mistral' },
      { id: 'codellama', name: 'Code Llama' },
      { id: 'phi', name: 'Phi' }
    ]
  } as const

  const defaultModels: DefaultModels = {
    openai: 'gpt-3.5-turbo',
    claude: 'claude-3-sonnet-20240229',
    moonshot: 'moonshot-v1-32k',
    gemini: 'gemini-2.0-flash',
    openrouter: 'openai/gpt-3.5-turbo',
    ollama: 'llama2'
  } as const;

  // 获取用户信息
  async function fetchUserInfo(userId: string, forceUpdate = false): Promise<UserInfo | null> {
    const now = Date.now()
    // 如果不是强制更新，且缓存未过期，直接返回
    if (!forceUpdate && now - lastUserInfoFetch < USER_INFO_MAX_AGE) {
      const { user_info } = await chrome.storage.local.get('user_info')
      if (user_info) {
        return user_info
      }
    }

    lastUserInfoFetch = now
    try {
      const response = await fetch(`${API_BASE_URL}/users/get-user`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId })
      })

      const data = await response.json()
      if (data.success && data.user) {
        await chrome.storage.local.set({
          user_info: data.user,
          user_info_timestamp: now
        })
        return data.user
      }
      return null
    } catch (error) {
      Logger.error('Error fetching user info:', error instanceof Error ? error.message : String(error))
      return null
    }
  }

  // 设置跳过登录状态
  async function setSkipLogin(skip: boolean): Promise<void> {
    try {
      skipLogin = skip
      await chrome.storage.sync.set({ [STORAGE_KEYS.SKIP_LOGIN]: skip })
      Logger.info('Skip login status updated:', skip)
    } catch (error) {
      Logger.error('Error setting skip login status:', error)
    }
  }

  // Handle Ollama request directly
  async function handleOllamaRequest(request: any, settings: Settings, model: string) {
    try {
      Logger.info('Handling Ollama request directly')

      const ollamaEndpoint = settings.ollama_endpoint || 'http://localhost:11434'

      // 构建完整的 prompt，包含表单填充的上下文
      const fullPrompt = buildFormFillPrompt(request)

      const response = await fetch(`${ollamaEndpoint}/v1/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ollama',
          'Accept': 'application/json'
        },
        body: JSON.stringify({
          model: model,
          messages: [
            { role: 'user', content: fullPrompt }
          ],
          stream: false
        })
      })

      if (!response.ok) {
        const errorText = await response.text().catch(() => 'Unknown error')
        if (response.status === 0 || response.status === 404) {
          throw new Error(`Cannot connect to Ollama server at ${ollamaEndpoint}. Please ensure Ollama is running.`)
        }
        throw new Error(`Ollama server error! status: ${response.status}, message: ${errorText}`)
      }

      const data = await response.json()
      Logger.success('Ollama request successful')

      // 解析 Ollama 的响应并转换为表单填充格式
      const content = data.choices[0].message.content
      const formData = parseFormFillResponse(content, request.options)

      return {
        success: true,
        data: formData
      }
    } catch (error: any) {
      Logger.error('Error in Ollama request:', error)
      throw error
    }
  }

  // Handle OpenAI request directly
  async function handleOpenAIRequest(apiKey: string, model: string, prompt: string) {
    try {
      Logger.info('Handling OpenAI request directly')

      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`
        },
        body: JSON.stringify({
          model: model,
          messages: [
            {
              role: 'system',
              content: 'You are a helpful assistant that generates form content based on descriptions. Always return valid JSON.'
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          stream: false,
          temperature: 0.7
        })
      })

      if (!response.ok) {
        const errorText = await response.text().catch(() => 'Unknown error')
        throw new Error(`OpenAI API error! status: ${response.status}, message: ${errorText}`)
      }

      const data = await response.json()
      Logger.success('OpenAI request successful')

      return {
        content: data.choices[0].message.content,
        usage: data.usage
      }
    } catch (error: any) {
      Logger.error('Error in OpenAI request:', error)
      throw error
    }
  }

  // Handle Claude request directly
  async function handleClaudeRequest(apiKey: string, model: string, prompt: string) {
    try {
      Logger.info('Handling Claude request directly')

      const response = await fetch('https://api.anthropic.com/v1/messages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': apiKey,
          'anthropic-version': '2023-06-01'
        },
        body: JSON.stringify({
          model: model,
          max_tokens: 1000,
          messages: [
            {
              role: 'user',
              content: prompt
            }
          ],
          system: 'You are a helpful assistant that generates form content based on descriptions. Always return valid JSON.'
        })
      })

      if (!response.ok) {
        const errorText = await response.text().catch(() => 'Unknown error')
        throw new Error(`Claude API error! status: ${response.status}, message: ${errorText}`)
      }

      const data = await response.json()
      Logger.success('Claude request successful')

      // Extract text content from the response
      let textContent = '';
      if (data.content && Array.isArray(data.content)) {
        for (const contentItem of data.content) {
          if (contentItem.type === 'text') {
            textContent += contentItem.text;
          }
        }
      }

      return {
        content: textContent,
        usage: {
          prompt_tokens: data.usage?.input_tokens || 0,
          completion_tokens: data.usage?.output_tokens || 0,
          total_tokens: (data.usage?.input_tokens || 0) + (data.usage?.output_tokens || 0)
        }
      }
    } catch (error: any) {
      Logger.error('Error in Claude request:', error)
      throw error
    }
  }

  // Handle Moonshot request directly
  async function handleMoonshotRequest(apiKey: string, model: string, prompt: string) {
    try {
      Logger.info('Handling Moonshot request directly')

      const response = await fetch('https://api.moonshot.cn/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`
        },
        body: JSON.stringify({
          model: model,
          messages: [
            {
              role: 'system',
              content: 'You are a helpful assistant that generates form content based on descriptions. Always return valid JSON.'
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          stream: false,
          temperature: 0.7
        })
      })

      if (!response.ok) {
        const errorText = await response.text().catch(() => 'Unknown error')
        throw new Error(`Moonshot API error! status: ${response.status}, message: ${errorText}`)
      }

      const data = await response.json()
      Logger.success('Moonshot request successful')

      return {
        content: data.choices[0].message.content,
        usage: data.usage
      }
    } catch (error: any) {
      Logger.error('Error in Moonshot request:', error)
      throw error
    }
  }

  // Handle Gemini request directly
  async function handleGeminiRequest(apiKey: string, model: string, prompt: string) {
    try {
      Logger.info('Handling Gemini request directly')

      // Use the correct API endpoint for Gemini 2.0
      const apiModel = model || 'gemini-2.0-flash';
      const response = await fetch(`https://generativelanguage.googleapis.com/v1/models/${apiModel}:generateContent?key=${apiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          contents: [
            {
              role: "user",
              parts: [
                {
                  text: `You are a helpful assistant that generates form content based on descriptions. Always return valid JSON object without any markdown formatting or additional text. Here's the task:\n\n${prompt}`
                }
              ]
            }
          ],
          generationConfig: {
            temperature: 0.7,
            topP: 0.8,
            topK: 40
          }
        })
      })

      if (!response.ok) {
        const errorText = await response.text().catch(() => 'Unknown error')
        throw new Error(`Gemini API error! status: ${response.status}, message: ${errorText}`)
      }

      const data = await response.json()
      Logger.success('Gemini request successful')

      if (!data.candidates?.[0]?.content?.parts?.[0]?.text) {
        throw new Error('Invalid response from Gemini API');
      }

      const rawContent = data.candidates[0].content.parts[0].text;

      // 尝试清理和解析内容
      try {
        // 移除可能的 markdown 标记
        let cleanContent = rawContent.replace(/^```json\s*/, '').replace(/```\s*$/, '');

        // 移除可能的注释
        cleanContent = cleanContent.replace(/\/\*[\s\S]*?\*\/|\/\/.*/g, '');

        // 尝试解析确保是有效的 JSON
        const parsedContent = JSON.parse(cleanContent);

        return {
          content: JSON.stringify(parsedContent),
          usage: {
            prompt_tokens: data.usageMetadata?.promptTokenCount || data.promptFeedback?.tokenCount?.promptTokens || 0,
            completion_tokens: data.usageMetadata?.candidatesTokenCount || 0,
            total_tokens: data.usageMetadata?.totalTokenCount || data.promptFeedback?.tokenCount?.totalTokens || 0
          }
        };
      } catch (error) {
        Logger.error('Gemini raw response:', rawContent);
        throw new Error('Failed to parse Gemini response as JSON');
      }
    } catch (error: any) {
      Logger.error('Error in Gemini request:', error)
      throw error
    }
  }

  // Handle DeepSeek request directly
  async function handleDeepSeekRequest(apiKey: string, model: string, prompt: string) {
    try {
      Logger.info('Handling DeepSeek request directly')

      const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`
        },
        body: JSON.stringify({
          model: model,
          messages: [
            {
              role: 'system',
              content: 'You are a helpful assistant that generates form content based on descriptions. Always return valid JSON.'
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          stream: false,
          temperature: 0.7
        })
      })

      if (!response.ok) {
        const errorText = await response.text().catch(() => 'Unknown error')
        throw new Error(`DeepSeek API error! status: ${response.status}, message: ${errorText}`)
      }

      const data = await response.json()
      Logger.success('DeepSeek request successful')

      return {
        content: data.choices[0].message.content,
        usage: data.usage
      }
    } catch (error: any) {
      Logger.error('Error in DeepSeek request:', error)
      throw error
    }
  }

  // Handle OpenRouter request directly
  async function handleOpenRouterRequest(apiKey: string, model: string, prompt: string) {
    try {
      Logger.info('Handling OpenRouter request directly')

      // 默认使用 OpenAI 的 GPT-3.5 Turbo 模型，但可以通过 model 参数指定其他模型
      // OpenRouter 支持多种模型，格式为 "provider/model"，例如：
      // - openai/gpt-4o
      // - anthropic/claude-3-opus
      // - google/gemini-pro
      // - meta-llama/llama-3-70b-instruct
      const routerModel = model || 'openai/gpt-3.5-turbo';

      const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`,
          'HTTP-Referer': 'https://fillify.tech',
          'X-Title': 'Fillify'
        },
        body: JSON.stringify({
          model: routerModel,
          messages: [
            {
              role: 'system',
              content: 'You are a helpful assistant that generates form content based on descriptions. Always return valid JSON without any markdown formatting or additional text.'
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          temperature: 0.7,
          max_tokens: 1000,
          response_format: { type: 'json_object' } // 请求 JSON 格式的响应
        })
      })

      if (!response.ok) {
        const errorText = await response.text().catch(() => 'Unknown error')
        throw new Error(`OpenRouter API error! status: ${response.status}, message: ${errorText}`)
      }

      const data = await response.json()
      Logger.success('OpenRouter request successful')

      if (!data.choices || data.choices.length === 0 || !data.choices[0].message.content) {
        throw new Error('Invalid response from OpenRouter API');
      }

      const rawContent = data.choices[0].message.content;

      // 尝试清理和解析内容
      try {
        // 移除可能的 markdown 标记
        let cleanContent = rawContent.replace(/^```json\s*/, '').replace(/```\s*$/, '');

        // 移除可能的注释
        cleanContent = cleanContent.replace(/\/\*[\s\S]*?\*\/|\/\/.*/g, '');

        // 尝试解析确保是有效的 JSON
        const parsedContent = JSON.parse(cleanContent);

        return {
          content: JSON.stringify(parsedContent),
          usage: {
            prompt_tokens: data.usage.prompt_tokens,
            completion_tokens: data.usage.completion_tokens,
            total_tokens: data.usage.total_tokens
          }
        };
      } catch (error) {
        Logger.error('OpenRouter raw response:', rawContent);
        throw new Error('Failed to parse OpenRouter response as JSON');
      }
    } catch (error: any) {
      Logger.error('Error in OpenRouter request:', error)
      throw error
    }
  }



  // 语言代码映射
  const languageCodeMap: { [key: string]: string } = {
    'id': 'Bahasa Indonesia',
    'ms': 'Bahasa Melayu',
    'da': 'Dansk',
    'de': 'Deutsch',
    'en': 'English',
    'es': 'Español',
    'fr': 'Français',
    'it': 'Italiano',
    'nl': 'Nederlands',
    'no': 'Norsk',
    'pl': 'Polski',
    'pt': 'Português',
    'ro': 'Română',
    'fi': 'Suomi',
    'sv': 'Svenska',
    'vi': 'Tiếng Việt',
    'tr': 'Türkçe',
    'hu': 'Magyar',
    'cs': 'Čeština',
    'uk': 'Українська',
    'ru': 'Русский',
    'bg': 'Български',
    'ar': 'العربية',
    'fa': 'فارسی',
    'he': 'עִבְרִית',
    'hi': 'हिन्दी',
    'th': 'ไทย',
    'ja': '日本語',
    'zh-CN': '中文（简体）',
    'zh-TW': '中文（繁體）',
    'ko': '한국어'
  };

  function getLanguageName(languageCode: string): string {
    return languageCodeMap[languageCode] || 'English';
  }

  function generateBugReportPromptWithProject(request: any): string {
    const { description, formFields, language, project } = request.options;
    // 只在非 auto 的情况下获取语言名称
    const languageName = language !== 'auto' ? getLanguageName(language) : null;
    const languageInstruction = language === 'auto'
      ? `First detect the language of this text: "${description}". Then generate ALL content using EXACTLY the same language as the detected language. For example, if the text is in Chinese, generate all content in Chinese; if the text is in English, generate all content in English, etc.`
      : `Generate all content in ${languageName}`;

    const projectContext = `Project Information:
- Project Name: ${project?.name || 'N/A'}
- Project Description: ${project?.description || 'N/A'}
- Environment Details:
${project?.environment || 'N/A'}

IMPORTANT: You MUST use these exact environment details in your response. Do not modify or omit any environment information.

Project Context:
1. This is a ${project?.description} named "${project?.name}"
2. The environment information above MUST be included exactly as shown in the environment section of your response
3. All generated content should be relevant to this specific project
`;

    return `As an experienced QA engineer specializing in bug reporting, analyze the provided bug description and generate structured content for a bug tracking form in JSON format. ${languageInstruction}.

Context:
${projectContext}
Bug Description: "${description}"

Form Fields (Field Names and Types):
${JSON.stringify(formFields, null, 2)}

Requirements:
1. ${languageInstruction}
2. Return a valid JSON object where keys match the field names/ids provided in the "Form Fields".
3. CRITICAL - Content Structure:
   - The description field should ONLY contain the template structure below
   - Do NOT write any description text before the template
   - Put ALL description content INSIDE the template sections
   - Follow the template structure EXACTLY

4. Field-specific instructions:
   - **Title fields**: Generate a concise and descriptive bug title that includes the project name
   - **Description fields**:
     - Put the COMPLETE bug description ONLY in the template's sections
     - Do not write any description text outside the template structure
     - Include all details (description, expected/actual behavior, impact) within the template sections
   - **Steps fields**: List reproducible steps in a numbered format
   - **Environment fields**: Use EXACTLY the environment information provided in project context
   - **Priority/Severity fields**: Assign a level based on the provided impact assessment
   - **Status fields**: Default to "New" or "Open"

5. Template Structure:
${project?.template || `### Environment
${project?.environment}

### Description
Write a detailed description of the bug here, including:
- Detailed bug description
- Impact assessment and severity level

### Steps to Reproduce
1. First specific step
2. Second specific step
3. Additional steps as needed

### Expected behavior
Describe what should happen in ${project?.name}

### Actual behavior
Describe what actually happens

### Reproduces how often
Specify how frequently the issue occurs

### Additional Information
Add any other relevant context about ${project?.name}`}

6. Output Requirements:
   - JSON object only (no extra text)
   - Match field types (e.g., string, number) in "Form Fields"
   - Include project-specific details where relevant
   - Use clear and professional language in ${language || 'English'}
   - Ensure sensitive data is anonymized
   - DO NOT write any content outside the template structure
   - Put ALL description content INSIDE the template sections

Return only the JSON object. Do not include any additional text or explanation.`;
  }

  function generateBugReportPromptWithoutProject(request: any): string {
    const { description, formFields, language } = request.options;
    // 只在非 auto 的情况下获取语言名称
    const languageName = language !== 'auto' ? getLanguageName(language) : null;
    const languageInstruction = language === 'auto'
      ? `First detect the language of this text: "${description}". Then generate ALL content using EXACTLY the same language as the detected language. For example, if the text is in Chinese, generate all content in Chinese; if the text is in English, generate all content in English, etc.`
      : `Generate all content in ${languageName}`;

    return `As an experienced QA engineer specializing in bug reporting, analyze the provided bug description and generate structured content for a bug tracking form in JSON format. ${languageInstruction}.

Bug Description: "${description}"

Form Fields (Field Names and Types):
${JSON.stringify(formFields, null, 2)}

Requirements:
1. ${languageInstruction}
2. Return a valid JSON object where keys match the field names/ids provided in the "Form Fields".
3. Field-specific instructions:
   - **Title fields**: Generate a concise and descriptive bug title
   - **Description fields**:
     - Put the COMPLETE bug description ONLY in the template's "Description" section
     - Do not write any description text outside the template structure
     - Include all details (description, expected/actual behavior, impact) within the template sections
   - **Steps fields**: List reproducible steps in a numbered format
   - **Environment fields**: Extract relevant technical details from the description
   - **Priority/Severity fields**: Assign a level based on the provided impact assessment
   - **Status fields**: Default to "New" or "Open"

4. CRITICAL - Content Structure:
   - The description field should ONLY contain the template structure below
   - Do NOT write any description text before the template
   - Put ALL description content INSIDE the template sections
   - Follow the template structure EXACTLY

5. Template Structure:

### Environment
- Version:
- Operating System:
- Browser: Any/Chrome/Safari/Firefox/Edge/Safari for iOS/Chrome for Android/...
- Operating System: Any/Windows/macOS/Linux/ChromeOS/...

### Description
Write a detailed description of the bug here, including:
- Detailed bug description
- Impact assessment and severity level

### Steps to Reproduce
1. First specific step
2. Second specific step
3. Additional steps as needed

### Expected behavior
Describe what you expect to happen

### Actual behavior
Describe what actually happens

### Reproduces how often
Specify how frequently the issue occurs

### Additional Information
Add any other relevant context or information

6. Output Requirements:
   - JSON object only (no extra text)
   - Match field types (e.g., string, number) in "Form Fields"
   - Keep generated content concise but informative
   - Use clear and professional language in ${language || 'English'}
   - Ensure sensitive data is anonymized
   - DO NOT write any content outside the template structure

Return only the JSON object. Do not include any additional text or explanation.`;
  }

  // 构建表单填充的 prompt
  function buildFormFillPrompt(request: any): string {
    const { options } = request
    const { description, formFields, mode, language } = options

    if (mode === 'bugReport') {
      return options.project
        ? generateBugReportPromptWithProject(request)
        : generateBugReportPromptWithoutProject(request);
    } else if (mode === 'email') {
      // 只在非 auto 的情况下获取语言名称
      const languageName = language !== 'auto' ? getLanguageName(language) : null;
      const languageInstruction = language === 'auto'
        ? `First detect the language of this text: "${description}". Then generate ALL content using EXACTLY the same language as the detected language.`
        : `Generate all content in ${languageName}`;

      return `As a professional email writing assistant, generate an email based on the user's description. ${languageInstruction}.

User Description: "${description}"

Form Fields:
${JSON.stringify(formFields, null, 2)}

Requirements:
1. Return a JSON object where keys match the field names/ids
2. Generate professional and appropriate email content
3. Keep the tone professional and courteous
4. Be concise but comprehensive

Return only the JSON object with no additional text.`;
    } else {
      // General mode
      // 只在非 auto 的情况下获取语言名称
      const languageName = language !== 'auto' ? getLanguageName(language) : null;
      const languageInstruction = language === 'auto'
        ? `First detect the language of this text: "${description}". Then generate ALL content using EXACTLY the same language as the detected language.`
        : `Generate all content in ${languageName}`;

      return `As a form filling assistant, generate appropriate content for this form based on the user's description. ${languageInstruction}.

User Description: "${description}"

Form Fields:
${JSON.stringify(formFields, null, 2)}

Requirements:
1. Return a JSON object where keys match the field names/ids
2. Generate natural, context-appropriate content for each field
3. Respect the field types (text, email, number, etc.)
4. Keep generated content concise but informative

Return only the JSON object with no additional text.`;
    }
  }

  // 清理AI响应内容
  function cleanAIResponse(content: string): string {
    try {
      // 移除可能的 markdown 标记
      content = content.replace(/^```json\n?/, '').replace(/\n?```$/, '');

      // 移除可能的注释
      content = content.replace(/\/\*[\s\S]*?\*\/|\/\/.*/g, '');

      const parsed = JSON.parse(content);
      return JSON.stringify(parsed);
    } catch (error) {
      throw new Error('Invalid JSON response from AI provider');
    }
  }

  // 解析表单填充响应
  function parseFormFillResponse(content: string, options: any) {
    try {
      // 先清理响应内容
      const cleanedContent = cleanAIResponse(content);
      const parsed = JSON.parse(cleanedContent);
      return parsed;
    } catch (error) {
      Logger.error('Error parsing form fill response:', error)

      // 尝试直接解析原始内容
      try {
        const jsonMatch = content.match(/\{[\s\S]*\}/)
        if (jsonMatch) {
          const parsed = JSON.parse(jsonMatch[0])
          return parsed
        }
      } catch (fallbackError) {
        Logger.error('Fallback parsing also failed:', fallbackError)
      }

      // 如果都失败了，根据模式返回适当的格式
      if (options.mode === 'email') {
        return {
          subject: 'Generated Email Subject',
          body: content
        }
      } else {
        return {
          content: content
        }
      }
    }
  }

  // 检查登录状态
  async function checkLoginStatus(): Promise<boolean> {
    try {
      if (!chrome.cookies) {
        Logger.error('Cookies API not available')
        return false
      }

      // 先检查是否跳过登录
      const { formify_skip_login } = await chrome.storage.sync.get(STORAGE_KEYS.SKIP_LOGIN)
      skipLogin = !!formify_skip_login

      const cookie = await chrome.cookies.get({
        url: COOKIE_URL,
        name: 'xToken'
      }).catch(error => {
        Logger.error('Error getting cookie:', error)
        return null
      })

      const newLoginState = !!cookie?.value

      if (newLoginState !== isLoggedIn) {
        isLoggedIn = newLoginState
        try {
          await chrome.runtime.sendMessage({
            type: 'loginStatusChanged',
            isLoggedIn,
            skipLogin,
            token: cookie?.value
          })
        } catch (error) {
          Logger.error('Error sending login status message:', error)
        }

        try {
          if (!isLoggedIn) {
            await chrome.storage.local.remove('user_info')
          } else if (cookie?.value) {
            await fetchUserInfo(cookie.value, true)
          }
        } catch (error) {
          Logger.error('Error updating user info:', error)
        }
      }

      return isLoggedIn
    } catch (error) {
      Logger.error('Error checking login status:', error)
      return false
    }
  }

  async function checkAndUpdateLoginStatus(): Promise<boolean> {
    try {
      const loginStatus = await checkLoginStatus()
      if (loginStatus) {
        const cookie = await chrome.cookies.get({
          url: COOKIE_URL,
          name: 'xToken'
        }).catch(() => null)

        if (cookie?.value) {
          await fetchUserInfo(cookie.value).catch(error => {
            Logger.error('Error fetching user info:', error)
          })
        }
      }
      return loginStatus
    } catch (error) {
      Logger.error('Error in checkAndUpdateLoginStatus:', error)
      return false
    }
  }

  // Handle AI request - Now all providers are handled locally
  async function handleAiRequest(request: any) {
    try {
      Logger.info('Handling AI request:', request)

      const storage = await chrome.storage.sync.get([
        STORAGE_KEYS.SETTINGS,
        STORAGE_KEYS.API_KEYS,
        STORAGE_KEYS.VALIDATED_KEYS
      ])

      const settings: Settings = storage[STORAGE_KEYS.SETTINGS] || {}
      const apiKeys: ApiKeys = storage[STORAGE_KEYS.API_KEYS] || {}
      const validatedKeys = storage[STORAGE_KEYS.VALIDATED_KEYS] || {}

      const provider = settings.defaultProvider || 'openai'

      // 检查 API Key 是否已验证
      if (!validatedKeys[provider]) {
        throw new Error(`No validated API key for ${provider}`)
      }

      const model = settings.defaultModel || defaultModels[provider as keyof DefaultModels]

      // 构建完整的 prompt，包含表单填充的上下文
      const fullPrompt = buildFormFillPrompt(request)

      // 根据不同的提供商直接调用对应的 API
      let aiResponse
      let usage = null

      switch (provider) {
        case 'ollama':
          return await handleOllamaRequest(request, settings, model)

        case 'openai':
          aiResponse = await handleOpenAIRequest(apiKeys[provider], model, fullPrompt)
          break

        case 'claude':
          aiResponse = await handleClaudeRequest(apiKeys[provider], model, fullPrompt)
          break

        case 'moonshot':
          aiResponse = await handleMoonshotRequest(apiKeys[provider], model, fullPrompt)
          break

        case 'gemini':
          aiResponse = await handleGeminiRequest(apiKeys[provider], model, fullPrompt)
          break

        case 'deepseek':
          aiResponse = await handleDeepSeekRequest(apiKeys[provider], model, fullPrompt)
          break

        case 'openrouter':
          aiResponse = await handleOpenRouterRequest(apiKeys[provider], model, fullPrompt)
          break

        default:
          throw new Error(`Unsupported provider: ${provider}`)
      }

      Logger.success('AI request successful:', aiResponse)

      // 解析 AI 的响应并转换为表单填充格式
      const formData = parseFormFillResponse(aiResponse.content, request.options)
      usage = aiResponse.usage

      const data = formData

      // 添加详细的数据结构日志
      Logger.info('AI response data structure:', {
        hasData: !!data,
        dataType: typeof data,
        keys: data ? Object.keys(data) : [],
        hasSuccess: data && 'success' in data,
        hasDataField: data && 'data' in data,
        dataFieldType: data && data.data ? typeof data.data : 'undefined',
        dataFieldKeys: data && data.data ? Object.keys(data.data) : [],
        hasUsage: data && data.data && 'usage' in data.data,
        usageStructure: data && data.data && data.data.usage ? Object.keys(data.data.usage) : [],
        isFormContent: data && typeof data === 'object' && (
          'to' in data ||
          'recipient' in data ||
          'subject' in data ||
          'body' in data ||
          'content' in data ||
          'title' in data ||
          'description' in data
        )
      })

      // 更新token使用统计 (跳过 Ollama，因为它是本地运行的)
      if (provider !== 'ollama') {
        try {
          // 获取当前token统计
          const tokenStatsStorage = await chrome.storage.sync.get(STORAGE_KEYS.TOKEN_STATS)
          const tokenStats = tokenStatsStorage[STORAGE_KEYS.TOKEN_STATS] || {}

          // 如果没有该provider的统计，初始化
          if (!tokenStats[provider]) {
            tokenStats[provider] = {
              promptTokens: 0,
              completionTokens: 0,
              totalTokens: 0,
              lastUpdated: new Date().toISOString()
            }
          }

          // 确保统计对象具有正确的结构
          if (!tokenStats[provider].promptTokens) tokenStats[provider].promptTokens = 0
          if (!tokenStats[provider].completionTokens) tokenStats[provider].completionTokens = 0
          if (!tokenStats[provider].totalTokens) tokenStats[provider].totalTokens = 0

          // 使用从 AI 服务商直接获取的 usage 数据
          if (usage) {
            let promptTokens = 0
            let completionTokens = 0
            let totalTokens = 0

            // 根据不同提供商的 usage 格式进行解析
            if (provider === 'openai' || provider === 'moonshot' || provider === 'deepseek' || provider === 'openrouter') {
              promptTokens = usage.prompt_tokens || 0
              completionTokens = usage.completion_tokens || 0
              totalTokens = usage.total_tokens || (promptTokens + completionTokens)
            } else if (provider === 'claude') {
              promptTokens = usage.input_tokens || 0
              completionTokens = usage.output_tokens || 0
              totalTokens = promptTokens + completionTokens
            } else if (provider === 'gemini') {
              promptTokens = usage.promptTokenCount || 0
              completionTokens = usage.candidatesTokenCount || 0
              totalTokens = usage.totalTokenCount || (promptTokens + completionTokens)
            }

            // 更新统计数据
            tokenStats[provider].promptTokens = (tokenStats[provider].promptTokens || 0) + promptTokens
            tokenStats[provider].completionTokens = (tokenStats[provider].completionTokens || 0) + completionTokens
            tokenStats[provider].totalTokens = (tokenStats[provider].totalTokens || 0) + totalTokens
            tokenStats[provider].lastUpdated = new Date().toISOString()

            Logger.info('Updated token stats with actual usage data:', {
              provider,
              promptTokens,
              completionTokens,
              totalTokens,
              accumulatedTotal: tokenStats[provider].totalTokens
            })
          } else {
            // 如果API没有返回usage数据，使用估算的token数量
            const promptLength = fullPrompt ? fullPrompt.length : 0
            const responseLength = aiResponse.content ? aiResponse.content.length : 0

            // 估算prompt tokens (每4个字符约为1个token)
            const estimatedPromptTokens = Math.ceil(promptLength / 4)
            // 估算completion tokens (每4个字符约为1个token)
            const estimatedCompletionTokens = Math.ceil(responseLength / 4)
            // 总token数
            const estimatedTotalTokens = estimatedPromptTokens + estimatedCompletionTokens

            // 更新统计数据
            tokenStats[provider].promptTokens = (tokenStats[provider].promptTokens || 0) + estimatedPromptTokens
            tokenStats[provider].completionTokens = (tokenStats[provider].completionTokens || 0) + estimatedCompletionTokens
            tokenStats[provider].totalTokens = (tokenStats[provider].totalTokens || 0) + estimatedTotalTokens
            tokenStats[provider].lastUpdated = new Date().toISOString()

            Logger.info('Updated token stats with estimated counts:', {
              provider,
              estimatedPromptTokens,
              estimatedCompletionTokens,
              estimatedTotalTokens,
              accumulatedTotal: tokenStats[provider].totalTokens
            })
          }

          // 保存更新后的统计
          await chrome.storage.sync.set({ [STORAGE_KEYS.TOKEN_STATS]: tokenStats })
          Logger.info('Token usage statistics updated for', provider, tokenStats[provider])
        } catch (statsError) {
          Logger.error('Error updating token statistics:', statsError)
          // 不影响主流程，继续返回AI响应
        }
      }

      return {
        success: true,
        data: data
      }

    } catch (error: any) {
      Logger.error('Error in AI request:', error)
      return {
        success: false,
        error: error.message || 'Failed to process AI request'
      }
    }
  }

  // 验证 API Key
  async function validateApiKey(provider: string, key: string) {
    try {
      let endpoint = '';
      let testPayload = {};

      switch (provider) {
        case 'openai':
          endpoint = 'https://api.openai.com/v1/chat/completions';
          testPayload = {
            model: "gpt-3.5-turbo",
            messages: [{ role: "user", content: "test" }],
            max_tokens: 1
          };
          break;

        case 'claude':
          endpoint = 'https://api.anthropic.com/v1/messages';
          testPayload = {
            model: "claude-3-sonnet-20240229",
            max_tokens: 1,
            messages: [{ role: "user", content: "test" }]
          };
          break;

        case 'moonshot':
          endpoint = 'https://api.moonshot.cn/v1/chat/completions';
          testPayload = {
            model: "moonshot-v1-8k",
            messages: [{ role: "user", content: "test" }],
            max_tokens: 1
          };
          break;

        case 'gemini':
          endpoint = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent';
          testPayload = {
            contents: [{ parts: [{ text: "test" }] }]
          };
          break;

        case 'deepseek':
          endpoint = 'https://api.deepseek.com/v1/chat/completions';
          testPayload = {
            model: "deepseek-chat",
            messages: [{ role: "user", content: "test" }],
            max_tokens: 1
          };
          break;

        case 'openrouter':
          endpoint = 'https://openrouter.ai/api/v1/chat/completions';
          testPayload = {
            model: "openai/gpt-3.5-turbo",
            messages: [{ role: "user", content: "test" }],
            max_tokens: 1
          };
          break;

        case 'ollama':
          // 对于 Ollama，我们测试连接到本地服务器
          const ollamaEndpoint = key || 'http://localhost:11434'; // key 作为端点地址
          endpoint = `${ollamaEndpoint}/v1/models`;
          // 对于 Ollama，我们只需要测试模型列表端点
          const modelsResponse = await fetch(endpoint, {
            method: 'GET',
            headers: {
              'Authorization': 'Bearer ollama',
              'Accept': 'application/json'
            }
          });

          if (!modelsResponse.ok) {
            if (modelsResponse.status === 0) {
              throw new Error(`Cannot connect to Ollama server at ${ollamaEndpoint}. Please check if Ollama is running and accessible.`);
            }
            throw new Error(`Ollama server responded with status ${modelsResponse.status}. Please check your Ollama installation.`);
          }

          return { success: true };

        default:
          throw new Error('Unsupported provider');
      }

      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      };

      // 根据不同提供商设置认证头
      switch (provider) {
        case 'openai':
          headers['Authorization'] = `Bearer ${key}`;
          break;
        case 'claude':
          headers['x-api-key'] = key;
          headers['anthropic-version'] = '2023-06-01';
          break;
        case 'moonshot':
          headers['Authorization'] = `Bearer ${key}`;
          break;
        case 'gemini':
          endpoint = `${endpoint}?key=${key}`;
          break;
        case 'deepseek':
          headers['Authorization'] = `Bearer ${key}`;
          break;
        case 'openrouter':
          headers['Authorization'] = `Bearer ${key}`;
          headers['HTTP-Referer'] = 'https://fillify.tech';
          headers['X-Title'] = 'Fillify';
          break;
      }

      const response = await fetch(endpoint, {
        method: 'POST',
        headers,
        body: JSON.stringify(testPayload)
      });

      const data = await response.json();

      // 检查响应是否包含错误
      if (response.status !== 200) {
        throw new Error(data.error?.message || 'Invalid API key');
      }

      return { success: true };
    } catch (error) {
      Logger.error('API key validation error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to validate API key'
      };
    }
  }

  // Event Listeners
  chrome.runtime.onInstalled.addListener(async (details) => {
    if (details.reason === 'install') {
      Logger.info('Extension installed')
      chrome.storage.sync.get(STORAGE_KEYS.SETTINGS, (data) => {
        if (!data[STORAGE_KEYS.SETTINGS]) {
          const defaultProvider = 'openai'
          const defaultModels = {
            openai: 'gpt-3.5-turbo',
            claude: 'claude-3-sonnet-20240229',
            moonshot: 'moonshot-v1-32k',
            gemini: 'gemini-2.0-flash',
            ollama: 'llama2'
          }

          chrome.storage.sync.set({
            [STORAGE_KEYS.SETTINGS]: {
              defaultProvider: defaultProvider,
              defaultModel: defaultModels[defaultProvider]
            }
          })
        }
      })

      await checkAndUpdateLoginStatus()
      chrome.tabs.create({
        url: chrome.runtime.getURL('onboarding.html')
      })
    } else if (details.reason === 'update') {
      Logger.info('Extension updated')
      chrome.storage.sync.get(STORAGE_KEYS.SETTINGS, (data) => {
        if (!data[STORAGE_KEYS.SETTINGS]) {
          const defaultProvider = 'openai'
          const defaultModels = {
            openai: 'gpt-3.5-turbo',
            claude: 'claude-3-sonnet-20240229',
            moonshot: 'moonshot-v1-32k',
            gemini: 'gemini-2.0-flash',
            ollama: 'llama2'
          }

          chrome.storage.sync.set({
            [STORAGE_KEYS.SETTINGS]: {
              defaultProvider: defaultProvider,
              defaultModel: defaultModels[defaultProvider]
            }
          })
        }
      })
    }
  })

  // 添加 cookie 监听功能
  chrome.cookies.onChanged.addListener(async (changeInfo: CookieChangeInfo) => {
    const { cookie, removed, cause } = changeInfo
    Logger.info('Cookie changed:', { cookie, removed, cause })

    if (cookie.domain.includes(COOKIE_DOMAIN) && cookie.name === 'xToken') {
      Logger.info('xToken cookie changed:', { value: cookie.value, removed })
      // 当 cookie 变化时，立即检查登录状态并强制更新用户信息
      const status = await checkLoginStatus()
      if (status && !removed) {
        await fetchUserInfo(cookie.value, true)
      }
    }
  })

  // 注册消息监听器
  chrome.runtime.onMessage.addListener((
    request: Message,
    sender: chrome.runtime.MessageSender,
    sendResponse: (response?: any) => void
  ) => {
    Logger.info('Received message:', request)

    if (request.type === 'getLoginStatus') {
      checkLoginStatus().then(status => {
        sendResponse({ isLoggedIn: status, skipLogin })
      })
      return true
    }

    if (request.type === 'setSkipLogin') {
      setSkipLogin(request.skip).then(() => {
        sendResponse({ success: true })
      }).catch(error => {
        Logger.error('Error setting skip login:', error)
        sendResponse({ success: false, error: String(error) })
      })
      return true
    }

    if (request.type === 'getUserInfo') {
      chrome.cookies.get({
        url: COOKIE_URL,
        name: 'xToken'
      }).then(async (cookie) => {
        if (cookie?.value) {
          const userInfo = await fetchUserInfo(cookie.value)
          sendResponse({ success: true, user: userInfo })
        } else {
          sendResponse({ success: false })
        }
      })
      return true
    }

    if (request.type === 'aiRequest') {
      handleAiRequest(request).then(response => {
        sendResponse(response)
      })
      return true
    }

    if (request.action === 'getSettings') {
      chrome.storage.sync.get([STORAGE_KEYS.SETTINGS, STORAGE_KEYS.API_KEYS], (data) => {
        sendResponse({
          settings: data[STORAGE_KEYS.SETTINGS] || {},
          apiKeys: data[STORAGE_KEYS.API_KEYS] || {}
        })
      })
      return true
    }

    if (request.action === 'updateSettings') {
      chrome.storage.sync.set({
        formify_settings: request.settings
      }, () => {
        sendResponse({ success: true })
      })
      return true
    }
  })
})
