<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Language Selection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #f9f9f9;
        }
        .form-field {
            margin: 10px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-sizing: border-box;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button {
            background-color: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #005a87;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background-color: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 4px;
        }
        .error {
            background-color: #ffe8e8;
            border-color: #f44336;
        }
        .language-test {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }
        .language-example {
            flex: 1;
            min-width: 300px;
        }
    </style>
</head>
<body>
    <h1>🌐 Language Selection Test for Fillify</h1>
    <p>This page tests whether the language selection feature is working correctly in the Fillify extension.</p>

    <div class="test-section">
        <h2>📝 Test Instructions</h2>
        <ol>
            <li>Open the Fillify extension popup</li>
            <li>Select a specific language (not "Auto")</li>
            <li>Enter a description in English</li>
            <li>Click "Fill Form" and observe if the generated content is in the selected language</li>
        </ol>
    </div>

    <div class="language-test">
        <div class="language-example">
            <div class="test-section">
                <h2>🇺🇸 English Test Form</h2>
                <p>Select <strong>English</strong> in the extension and use description: "Create a bug report for login issues"</p>
                
                <div class="form-field">
                    <label for="en-title">Bug Title:</label>
                    <input type="text" id="en-title" name="title" placeholder="Bug title will appear here">
                </div>
                
                <div class="form-field">
                    <label for="en-description">Description:</label>
                    <textarea id="en-description" name="description" placeholder="Bug description will appear here"></textarea>
                </div>
                
                <div class="form-field">
                    <label for="en-steps">Steps to Reproduce:</label>
                    <textarea id="en-steps" name="steps" placeholder="Steps will appear here"></textarea>
                </div>
                
                <div class="form-field">
                    <label for="en-environment">Environment:</label>
                    <input type="text" id="en-environment" name="environment" placeholder="Environment info will appear here">
                </div>
            </div>
        </div>

        <div class="language-example">
            <div class="test-section">
                <h2>🇨🇳 Chinese Test Form</h2>
                <p>Select <strong>中文（简体）</strong> in the extension and use description: "创建一个登录问题的错误报告"</p>
                
                <div class="form-field">
                    <label for="zh-title">错误标题:</label>
                    <input type="text" id="zh-title" name="title" placeholder="错误标题将出现在这里">
                </div>
                
                <div class="form-field">
                    <label for="zh-description">描述:</label>
                    <textarea id="zh-description" name="description" placeholder="错误描述将出现在这里"></textarea>
                </div>
                
                <div class="form-field">
                    <label for="zh-steps">重现步骤:</label>
                    <textarea id="zh-steps" name="steps" placeholder="步骤将出现在这里"></textarea>
                </div>
                
                <div class="form-field">
                    <label for="zh-environment">环境:</label>
                    <input type="text" id="zh-environment" name="environment" placeholder="环境信息将出现在这里">
                </div>
            </div>
        </div>
    </div>

    <div class="language-test">
        <div class="language-example">
            <div class="test-section">
                <h2>🇪🇸 Spanish Test Form</h2>
                <p>Select <strong>Español</strong> in the extension and use description: "Crear un reporte de error para problemas de inicio de sesión"</p>
                
                <div class="form-field">
                    <label for="es-title">Título del Error:</label>
                    <input type="text" id="es-title" name="title" placeholder="El título del error aparecerá aquí">
                </div>
                
                <div class="form-field">
                    <label for="es-description">Descripción:</label>
                    <textarea id="es-description" name="description" placeholder="La descripción del error aparecerá aquí"></textarea>
                </div>
                
                <div class="form-field">
                    <label for="es-steps">Pasos para Reproducir:</label>
                    <textarea id="es-steps" name="steps" placeholder="Los pasos aparecerán aquí"></textarea>
                </div>
                
                <div class="form-field">
                    <label for="es-environment">Entorno:</label>
                    <input type="text" id="es-environment" name="environment" placeholder="La información del entorno aparecerá aquí">
                </div>
            </div>
        </div>

        <div class="language-example">
            <div class="test-section">
                <h2>🇯🇵 Japanese Test Form</h2>
                <p>Select <strong>日本語</strong> in the extension and use description: "ログイン問題のバグレポートを作成する"</p>
                
                <div class="form-field">
                    <label for="ja-title">バグタイトル:</label>
                    <input type="text" id="ja-title" name="title" placeholder="バグタイトルがここに表示されます">
                </div>
                
                <div class="form-field">
                    <label for="ja-description">説明:</label>
                    <textarea id="ja-description" name="description" placeholder="バグの説明がここに表示されます"></textarea>
                </div>
                
                <div class="form-field">
                    <label for="ja-steps">再現手順:</label>
                    <textarea id="ja-steps" name="steps" placeholder="手順がここに表示されます"></textarea>
                </div>
                
                <div class="form-field">
                    <label for="ja-environment">環境:</label>
                    <input type="text" id="ja-environment" name="environment" placeholder="環境情報がここに表示されます">
                </div>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>🔍 Auto Language Detection Test</h2>
        <p>Keep the extension language setting as <strong>"Auto"</strong> and test with different input languages:</p>
        
        <div class="form-field">
            <label for="auto-title">Title:</label>
            <input type="text" id="auto-title" name="title" placeholder="Title will appear here">
        </div>
        
        <div class="form-field">
            <label for="auto-description">Description:</label>
            <textarea id="auto-description" name="description" placeholder="Description will appear here"></textarea>
        </div>
        
        <h3>Test Cases for Auto Detection:</h3>
        <ul>
            <li><strong>English:</strong> "The login button is not working on the homepage"</li>
            <li><strong>Chinese:</strong> "登录按钮在首页无法正常工作"</li>
            <li><strong>Spanish:</strong> "El botón de inicio de sesión no funciona en la página principal"</li>
            <li><strong>Japanese:</strong> "ホームページのログインボタンが動作しません"</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>✅ Expected Results</h2>
        <ul>
            <li><strong>Specific Language Selection:</strong> Generated content should be in the selected language regardless of input language</li>
            <li><strong>Auto Detection:</strong> Generated content should match the language of the input description</li>
            <li><strong>Consistency:</strong> All form fields should be filled in the same language</li>
            <li><strong>Quality:</strong> Generated content should be natural and professional in the target language</li>
        </ul>
    </div>

    <script>
        // Add some basic form interaction for testing
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Language selection test page loaded');
            console.log('Ready for Fillify extension testing');
        });
    </script>
</body>
</html>
