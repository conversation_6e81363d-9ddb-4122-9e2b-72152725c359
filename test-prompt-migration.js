// Test script to verify the prompt migration from server-side to local processing
// This script tests the new prompt generation logic

console.log('🧪 Testing Prompt Migration...');

// Test data similar to what would be sent from content script
const testRequests = [
  {
    type: 'aiRequest',
    prompt: 'Test bug report',
    options: {
      mode: 'bugReport',
      description: 'The login button is not working properly on the homepage',
      formFields: [
        { name: 'title', type: 'text', label: 'Bug Title' },
        { name: 'description', type: 'textarea', label: 'Description' },
        { name: 'steps', type: 'textarea', label: 'Steps to Reproduce' },
        { name: 'environment', type: 'text', label: 'Environment' }
      ],
      language: 'en',
      project: {
        name: 'Test Project',
        description: 'A test web application',
        environment: 'Chrome 120, Windows 11, Node.js 18.x'
      }
    }
  },
  {
    type: 'aiRequest',
    prompt: 'Test email',
    options: {
      mode: 'email',
      description: 'Write a professional email to request a meeting with the team lead',
      formFields: [
        { name: 'to', type: 'email', label: 'To' },
        { name: 'subject', type: 'text', label: 'Subject' },
        { name: 'body', type: 'textarea', label: 'Message Body' }
      ],
      language: 'en'
    }
  },
  {
    type: 'aiRequest',
    prompt: 'Test general form',
    options: {
      mode: 'general',
      description: 'Fill out a contact form for a software development inquiry',
      formFields: [
        { name: 'name', type: 'text', label: 'Full Name' },
        { name: 'email', type: 'email', label: 'Email Address' },
        { name: 'company', type: 'text', label: 'Company' },
        { name: 'message', type: 'textarea', label: 'Message' }
      ],
      language: 'en'
    }
  }
];

// Test language detection
const languageTestRequest = {
  type: 'aiRequest',
  prompt: 'Test auto language detection',
  options: {
    mode: 'bugReport',
    description: '登录按钮在首页无法正常工作',
    formFields: [
      { name: 'title', type: 'text', label: 'Bug Title' },
      { name: 'description', type: 'textarea', label: 'Description' }
    ],
    language: 'auto'
  }
};

async function testPromptGeneration() {
  console.log('📝 Testing prompt generation logic...');
  
  try {
    // Test each request type
    for (let i = 0; i < testRequests.length; i++) {
      const request = testRequests[i];
      console.log(`\n🔍 Testing ${request.options.mode} mode...`);
      
      // Send the request to background script
      const response = await new Promise((resolve, reject) => {
        chrome.runtime.sendMessage(request, (response) => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else {
            resolve(response);
          }
        });
      });
      
      console.log(`✅ ${request.options.mode} mode response:`, response.success ? 'SUCCESS' : 'FAILED');
      if (response.success) {
        console.log('📊 Response data keys:', Object.keys(response.data || {}));
      } else {
        console.log('❌ Error:', response.error);
      }
    }
    
    // Test language auto-detection
    console.log('\n🌐 Testing auto language detection...');
    const langResponse = await new Promise((resolve, reject) => {
      chrome.runtime.sendMessage(languageTestRequest, (response) => {
        if (chrome.runtime.lastError) {
          reject(new Error(chrome.runtime.lastError.message));
        } else {
          resolve(response);
        }
      });
    });
    
    console.log('✅ Language detection response:', langResponse.success ? 'SUCCESS' : 'FAILED');
    if (langResponse.success) {
      console.log('📊 Response data keys:', Object.keys(langResponse.data || {}));
    } else {
      console.log('❌ Error:', langResponse.error);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Check if we have valid API keys configured
async function checkApiConfiguration() {
  console.log('🔑 Checking API configuration...');
  
  const storage = await chrome.storage.sync.get(['formify_settings', 'formify_api_keys', 'formify_validated_keys']);
  const settings = storage.formify_settings || {};
  const apiKeys = storage.formify_api_keys || {};
  const validatedKeys = storage.formify_validated_keys || {};
  
  console.log('⚙️  Current provider:', settings.defaultProvider || 'Not set');
  console.log('🔐 Available API keys:', Object.keys(apiKeys));
  console.log('✅ Validated keys:', Object.keys(validatedKeys));
  
  if (!settings.defaultProvider) {
    console.log('⚠️  No default provider set. Please configure in settings.');
    return false;
  }
  
  if (!validatedKeys[settings.defaultProvider]) {
    console.log(`⚠️  No validated API key for ${settings.defaultProvider}. Please validate your API key.`);
    return false;
  }
  
  return true;
}

// Main test function
async function runTests() {
  console.log('🚀 Starting Prompt Migration Tests...\n');
  
  const hasValidConfig = await checkApiConfiguration();
  
  if (hasValidConfig) {
    await testPromptGeneration();
  } else {
    console.log('❌ Cannot run tests without valid API configuration.');
  }
  
  console.log('\n✨ Test completed!');
}

// Run tests when script loads
runTests().catch(console.error);
